.modern-interactive-transcript {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Empty State */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #6b7280;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-state h3 {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #374151;
}

.empty-state p {
  font-size: 14px;
  line-height: 1.5;
}

/* Header */
.transcript-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid #e5e7eb;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.transcript-title {
  font-size: 18px;
  font-weight: 700;
  color: #1f2937;
  margin: 0;
}

.segment-count {
  background: #e0e7ff;
  color: #3730a3;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.header-controls {
  display: flex;
  gap: 8px;
}

.control-toggle {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  background: white;
  border-radius: 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #374151;
}

.control-toggle:hover {
  background: #f9fafb;
  border-color: #9ca3af;
  transform: translateY(-1px);
}

.control-toggle.active {
  background: #3b82f6;
  border-color: #3b82f6;
  color: white;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.control-toggle.edit-mode.active {
  background: #ef4444;
  border-color: #ef4444;
}

.toggle-icon {
  font-size: 14px;
}

/* Search */
.search-container {
  padding: 16px 24px;
  border-bottom: 1px solid #e5e7eb;
  background: #fafbfc;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 12px;
  font-size: 14px;
  color: #6b7280;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 10px 12px 10px 36px;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.clear-search {
  position: absolute;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  background: #f3f4f6;
  border-radius: 50%;
  font-size: 12px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: #e5e7eb;
  color: #374151;
}

/* Transcript List */
.transcript-list {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.transcript-segment {
  margin-bottom: 8px;
  border-radius: 12px;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 2px solid transparent;
}

.transcript-segment:hover {
  background: #f8fafc;
  border-color: #e2e8f0;
}

.transcript-segment.active {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-color: #3b82f6;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.transcript-segment.edit-mode {
  border-color: #fbbf24;
}

.transcript-segment.editing {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-color: #f59e0b;
  cursor: default;
}

/* Segment Content */
.segment-content {
  padding: 16px 20px;
}

.segment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.segment-time {
  background: #f1f5f9;
  color: #475569;
  padding: 4px 8px;
  border-radius: 6px;
  font-family: 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
  font-size: 12px;
  font-weight: 600;
}

.transcript-segment.active .segment-time {
  background: #3b82f6;
  color: white;
}

.segment-actions {
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.transcript-segment:hover .segment-actions,
.transcript-segment.edit-mode .segment-actions {
  opacity: 1;
}

.edit-btn,
.repeat-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

.edit-btn:hover {
  background: #fbbf24;
  transform: scale(1.1);
}

.repeat-btn:hover {
  background: #10b981;
  transform: scale(1.1);
}

.segment-text {
  font-size: 15px;
  line-height: 1.6;
  color: #1f2937;
  word-wrap: break-word;
}

.search-highlight {
  background: #fef08a;
  color: #92400e;
  padding: 1px 2px;
  border-radius: 2px;
}

.speaker-label {
  margin-top: 8px;
  font-size: 12px;
  color: #6366f1;
  font-weight: 600;
  background: #e0e7ff;
  padding: 2px 6px;
  border-radius: 4px;
  display: inline-block;
}

/* Segment Editor */
.segment-editor {
  padding: 20px;
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  border-radius: 8px;
}

.editor-header {
  margin-bottom: 16px;
}

.editor-title {
  font-size: 14px;
  font-weight: 600;
  color: #92400e;
}

.time-inputs {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.time-input-group,
.text-input-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.time-input-group label,
.text-input-group label {
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

.time-input,
.text-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  transition: all 0.2s ease;
}

.time-input:focus,
.text-input:focus {
  outline: none;
  border-color: #f59e0b;
  box-shadow: 0 0 0 3px rgba(245, 158, 11, 0.1);
}

.text-input {
  resize: vertical;
  min-height: 60px;
  font-family: inherit;
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 8px;
  margin-top: 16px;
}

.btn-cancel,
.btn-save {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: #f3f4f6;
  color: #374151;
}

.btn-cancel:hover {
  background: #e5e7eb;
}

.btn-save {
  background: #10b981;
  color: white;
}

.btn-save:hover {
  background: #059669;
  transform: translateY(-1px);
}

/* Instructions */
.transcript-instructions {
  padding: 12px 24px;
  background: #f8fafc;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #6b7280;
}

.instruction-item {
  display: flex;
  align-items: center;
  gap: 4px;
}

.instruction-icon {
  font-size: 14px;
}

/* Scrollbar Styling */
.transcript-list::-webkit-scrollbar {
  width: 6px;
}

.transcript-list::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.transcript-list::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.transcript-list::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Responsive Design */
@media (max-width: 768px) {
  .transcript-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .header-controls {
    justify-content: center;
  }
  
  .time-inputs {
    grid-template-columns: 1fr;
  }
  
  .transcript-instructions {
    flex-direction: column;
    gap: 8px;
  }
}
